'use client';
import { useEffect, useRef, useState, useCallback } from 'react';
import * as faceapi from 'face-api.js';

const LOCAL_STORAGE_STUDENTS_KEY = 'face-attendance-students-temp';
const LOCAL_STORAGE_ATTENDANCE_KEY = 'face-attendance-records-temp';

type Student = {
  name: string;
  descriptors: number[][];
  photo: string;
};

type AttendanceRecord = {
  student: string;
  timeIn: Date;
  timeOut: Date | null;
};

export default function TempFaceAttendanceSystem() {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const registerPhotoRef = useRef<HTMLCanvasElement>(null);
  const actionLockRef = useRef(0); // Timestamp for preventing rapid button clicks

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCameraOn, setIsCameraOn] = useState(false);
  const [students, setStudents] = useState<Student[]>([]);
  const [attendance, setAttendance] = useState<AttendanceRecord[]>([]);
  const [registerMode, setRegisterMode] = useState(false);
  const [newStudentName, setNewStudentName] = useState('');
  const [modelsLoaded, setModelsLoaded] = useState(false);
  const [recognizedFace, setRecognizedFace] = useState<string | null>(null);
  const [recognizedAccuracy, setRecognizedAccuracy] = useState<number | null>(null);
  const [registrationCount, setRegistrationCount] = useState(0);
  const [tempDescriptors, setTempDescriptors] = useState<number[][]>([]);

  useEffect(() => {
    const loadSavedData = () => {
      try {
        const savedStudents = localStorage.getItem(LOCAL_STORAGE_STUDENTS_KEY);
        if (savedStudents) {
          const parsedStudents = JSON.parse(savedStudents);
          setStudents(parsedStudents);
          console.log('Loaded', parsedStudents.length, 'students from temp localStorage');
        }
        const savedAttendance = localStorage.getItem(LOCAL_STORAGE_ATTENDANCE_KEY);
        if (savedAttendance) {
          const parsedAttendance = JSON.parse(savedAttendance);
          const attendanceWithDates = parsedAttendance.map((record: any) => ({
            student: record.student,
            timeIn: new Date(record.timeIn),
            timeOut: record.timeOut ? new Date(record.timeOut) : null
          }));
          setAttendance(attendanceWithDates);
          console.log('Loaded', attendanceWithDates.length, 'temp attendance records from localStorage');
        }
      } catch (err) {
        console.error('Error loading saved data (temp):', err);
      }
    };
    loadSavedData();
  }, []);

  useEffect(() => {
    const loadModels = async () => {
      try {
        setError(null);
        console.log("Loading models...");
        await Promise.all([
          faceapi.nets.tinyFaceDetector.loadFromUri('/models'),
          faceapi.nets.faceLandmark68Net.loadFromUri('/models'),
          faceapi.nets.faceRecognitionNet.loadFromUri('/models'),
          faceapi.nets.ssdMobilenetv1.loadFromUri('/models')
        ]);
        setModelsLoaded(true);
        setIsLoading(false);
        console.log('All models loaded successfully');
        setError('✅ Face detection models loaded successfully');
        setTimeout(() => setError(null), 3000);
      } catch (err) {
        console.error('Failed to load models:', err);
        setError('Failed to load face detection models. Please refresh the page.');
        setIsLoading(false);
      }
    };
    loadModels();
    return () => {
      console.log("TempFaceAttendanceSystem unmounting: Calling stopCamera()");
      stopCamera();
    };
  }, []);

  const handleStartCamera = async () => {
    setError(null);
    if (videoRef.current && videoRef.current.srcObject) {
      (videoRef.current.srcObject as MediaStream).getTracks().forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
    setIsCameraOn(false);
    try {
      console.log("Requesting camera stream...");
      const stream = await navigator.mediaDevices.getUserMedia({ video: { width: 640, height: 480 } });
      console.log("Camera stream acquired.");
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        await new Promise<void>((resolve, reject) => {
          if (!videoRef.current) {
            console.error("handleStartCamera: videoRef became null during promise setup.");
            reject(new Error("Video ref null"));
            return;
          }
          videoRef.current.onloadedmetadata = () => {
            console.log("Video metadata loaded. Attempting to play...");
            videoRef.current?.play().then(() => {
              console.log('Video playback started successfully.');
              setIsCameraOn(true);
              setError("✅ Camera started successfully");
              setTimeout(() => setError(null), 2000);
              resolve();
            }).catch(playError => {
              console.error('Error attempting to play video:', playError);
              setError('Camera stream acquired, but video playback failed.');
              stream.getTracks().forEach(track => track.stop());
              if (videoRef.current) videoRef.current.srcObject = null;
              reject(playError);
            });
          };
          videoRef.current.onerror = (e) => {
            console.error("Video element error:", e);
            setError("An error occurred with the video element.");
            stream.getTracks().forEach(track => track.stop());
            if (videoRef.current) videoRef.current.srcObject = null;
            reject(new Error("Video element error"));
          };
        });
      } else {
        throw new Error("Video element reference is not available.");
      }
    } catch (err: any) {
      console.error('Failed to start camera:', err);
      setError(`Failed to start camera: ${err.message || 'Check permissions.'}`);
      setIsCameraOn(false);
      if (videoRef.current && videoRef.current.srcObject) {
        (videoRef.current.srcObject as MediaStream).getTracks().forEach(track => track.stop());
        videoRef.current.srcObject = null;
      }
    }
  };

  const stopCamera = () => {
    console.log("Attempting to stop camera...");
    if (videoRef.current?.srcObject) {
      (videoRef.current.srcObject as MediaStream).getTracks().forEach(track => {
        console.log(`Stopping track: ${track.label || track.kind}`);
        track.stop();
      });
      videoRef.current.srcObject = null;
      console.log("Camera stream stopped and srcObject nullified.");
      setError("Camera stopped");
      setTimeout(() => setError(null), 2000);
    } else {
      console.log("No active stream or videoRef to stop.");
    }
    setIsCameraOn(false);
    // Clear overlay when camera stops
    if (canvasRef.current) {
        const overlayCtx = canvasRef.current.getContext('2d');
        if (overlayCtx) {
            overlayCtx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);
        }
    }
  };

  const handleMarkAttendanceClick = async () => {
    const now = Date.now();
    if (now - actionLockRef.current < 1500) { // 1.5 second cooldown
      console.log("handleMarkAttendanceClick: Called too soon. Ignoring.");
      setError("Please wait a moment before trying again.");
      setTimeout(() => setError(null), 1500);
      return;
    }
    actionLockRef.current = now;

    if (!videoRef.current || !isCameraOn || !videoRef.current.srcObject || videoRef.current.paused || videoRef.current.ended) {
      setError("Camera is not ready. Please start the camera.");
      setTimeout(() => setError(null), 3000);
      return;
    }
    const video = videoRef.current;
    if (video.readyState < HTMLMediaElement.HAVE_ENOUGH_DATA || video.videoWidth === 0 || video.videoHeight === 0) {
      setError("Video stream not fully ready. Please wait a moment.");
      setTimeout(() => setError(null), 3000);
      return;
    }

    const overlayCanvas = canvasRef.current;
    if (!overlayCanvas) {
      setError("Overlay canvas not available.");
      setTimeout(() => setError(null), 3000);
      return;
    }
    if (overlayCanvas.width !== video.videoWidth || overlayCanvas.height !== video.videoHeight) {
      overlayCanvas.width = video.videoWidth;
      overlayCanvas.height = video.videoHeight;
    }
    const overlayCtx = overlayCanvas.getContext('2d');
    if (!overlayCtx) {
      setError("Cannot draw on overlay.");
      setTimeout(() => setError(null), 3000);
      return;
    }

    if (students.length === 0) {
      setError("No students registered. Please register students first.");
      setTimeout(() => setError(null), 3000);
      return;
    }

    setError("Processing... Please look at the camera.");

    try {
      const snapshotCanvas = document.createElement('canvas');
      snapshotCanvas.width = video.videoWidth;
      snapshotCanvas.height = video.videoHeight;
      const snapshotCtx = snapshotCanvas.getContext('2d');
      if (!snapshotCtx) {
        setError("Failed to create snapshot for detection.");
        setTimeout(() => setError(null), 3000);
        return;
      }
      snapshotCtx.drawImage(video, 0, 0, snapshotCanvas.width, snapshotCanvas.height);

      overlayCtx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);
      overlayCtx.drawImage(video, 0, 0, overlayCanvas.width, overlayCanvas.height);
      
      const detections = await faceapi.detectAllFaces(snapshotCanvas, new faceapi.SsdMobilenetv1Options({ minConfidence: 0.5 }))
        .withFaceLandmarks().withFaceDescriptors();

      if (!detections || detections.length === 0) {
        setError("No faces detected in the frame. Please ensure clear view and good lighting.");
        setTimeout(() => setError(null), 3000);
        return;
      }

      const labeledDescriptors = students.map(student => new faceapi.LabeledFaceDescriptors(student.name, student.descriptors.map(d => new Float32Array(d))));
      const faceMatcher = new faceapi.FaceMatcher(labeledDescriptors, 0.5);
      let foundAndMarked = false;
      let recognizedStudentName: string | null = null;

      detections.forEach(d => {
        const bestMatch = faceMatcher.findBestMatch(d.descriptor);
        const box = d.detection.box;
        const isRecognizedMatch = bestMatch.label !== 'unknown';

        console.log(`[Mark Attendance] Processed face. Recognized: ${isRecognizedMatch}, Label: ${bestMatch.label}, Distance: ${bestMatch.distance.toFixed(4)}`);

        overlayCtx.lineWidth = 3;
        overlayCtx.strokeStyle = isRecognizedMatch ? 'rgb(0, 255, 0)' : 'rgb(255, 0, 0)';
        overlayCtx.fillStyle = isRecognizedMatch ? 'rgba(0, 255, 0, 0.2)' : 'rgba(255, 0, 0, 0.2)';
        overlayCtx.beginPath();
        overlayCtx.rect(box.x, box.y, box.width, box.height);
        overlayCtx.stroke();
        overlayCtx.fill();
        
        overlayCtx.fillStyle = isRecognizedMatch ? 'rgba(0, 255, 0, 0.8)' : 'rgba(255, 0, 0, 0.8)';
        overlayCtx.fillRect(box.x, box.y - 25, Math.max(box.width, (isRecognizedMatch ? bestMatch.label.length * 8 + 20 : 140)), 25);
        overlayCtx.fillStyle = 'white';
        overlayCtx.font = '16px Arial';
        overlayCtx.fillText(isRecognizedMatch ? bestMatch.label : 'No student found', box.x + 5, box.y - 7);

        if (isRecognizedMatch && !foundAndMarked) { // Process only the first valid match
          recognizedStudentName = bestMatch.label;
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          const existingRecord = attendance.find(a => a.student === bestMatch.label && new Date(a.timeIn).setHours(0,0,0,0) === today.getTime());
          
          if (!existingRecord) {
            const recordTime = new Date();
            const newRecord = { student: bestMatch.label, timeIn: recordTime, timeOut: null };
            setAttendance(prev => {
              const updated = [...prev, newRecord];
              localStorage.setItem(LOCAL_STORAGE_ATTENDANCE_KEY, JSON.stringify(updated));
              return updated;
            });
            setRecognizedFace(bestMatch.label);
            setRecognizedAccuracy(1 - bestMatch.distance);
            setTimeout(() => { setRecognizedFace(null); setRecognizedAccuracy(null); }, 3000);
            setError(`✅ Attendance marked for ${bestMatch.label} at ${recordTime.toLocaleTimeString()}. Accuracy: ${((1 - bestMatch.distance) * 100).toFixed(2)}%`);
            setTimeout(() => setError(null), 4000);
            foundAndMarked = true;
          } else {
            setError(`✅ ${bestMatch.label} already marked in today.`);
            setTimeout(() => setError(null), 3000);
            foundAndMarked = true; 
          }
        }
      });

      if (!foundAndMarked && detections.length > 0) { 
        setError("No registered student recognized in this frame.");
        setTimeout(() => setError(null), 3000);
      }
    } catch (err) {
      console.error('Mark Attendance error:', err);
      setError("An error occurred during attendance marking.");
      setTimeout(() => setError(null), 3000);
    }
  };

  const captureRegistrationPhoto = async () => {
    if (!videoRef.current || !registerPhotoRef.current || !isCameraOn) {
        setError("Camera must be on to capture photo.");
        return null;
    }
    const video = videoRef.current;
    const canvas = registerPhotoRef.current;
    canvas.width = video.videoWidth || 640;
    canvas.height = video.videoHeight || 480;
    const ctx = canvas.getContext('2d');
    if (!ctx) return null;
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    console.log('Photo captured for registration, canvas size:', canvas.width, 'x', canvas.height);
    return canvas;
  };

  const registerStudent = async () => {
    if (!newStudentName.trim()) {
      setError('Please enter a student name');
      return;
    }
    try {
      const canvas = await captureRegistrationPhoto();
      if (!canvas) {
        setError('Failed to capture photo. Ensure camera is on and ready.');
        return;
      }
      console.log('Attempting to detect face for registration...');
      const detection = await faceapi.detectSingleFace(canvas, new faceapi.SsdMobilenetv1Options({ minConfidence: 0.5 }))
        .withFaceLandmarks().withFaceDescriptor();
      if (!detection) {
        setError('No face detected in the photo. Please ensure your face is clearly visible and well-lit.');
        return;
      }
      console.log('Successfully extracted face descriptor for registration.');
      if (students.length > 0) {
        const existingDescriptors = students.map(student => new faceapi.LabeledFaceDescriptors(student.name, student.descriptors.map(d => new Float32Array(d))));
        const faceMatcher = new faceapi.FaceMatcher(existingDescriptors, 0.4);
        const bestMatch = faceMatcher.findBestMatch(detection.descriptor);
        if (bestMatch.label !== 'unknown') {
          setError(`This face looks similar to registered student "${bestMatch.label}". Please try again or use a different name.`);
          return;
        }
      }
      if (registrationCount === 0) {
        setTempDescriptors([Array.from(detection.descriptor)]);
        setRegistrationCount(1);
        setError(`✅ Captured pose 1/3. Please move your head slightly and press Register again.`);
        return;
      }
      if (registrationCount === 1) {
        setTempDescriptors(prev => [...prev, Array.from(detection.descriptor)]);
        setRegistrationCount(2);
        setError(`✅ Captured pose 2/3. One more - please move your head slightly and press Register again.`);
        return;
      }
      if (registrationCount === 2) {
        const allDescriptors = [...tempDescriptors, Array.from(detection.descriptor)];
        const newStudentData: Student = {
          name: newStudentName.trim(),
          descriptors: allDescriptors,
          photo: canvas.toDataURL('image/jpeg')
        };
        console.log('Registering new student:', newStudentData.name, 'with', allDescriptors.length, 'face descriptors');
        const updatedStudents = [...students, newStudentData];
        setStudents(updatedStudents);
        localStorage.setItem(LOCAL_STORAGE_STUDENTS_KEY, JSON.stringify(updatedStudents));
        setNewStudentName('');
        setRegisterMode(false);
        setRegistrationCount(0);
        setTempDescriptors([]);
        setError(`✅ Successfully registered ${newStudentData.name} with 3 face poses!`);
        setTimeout(() => setError(null), 3000);
      }
    } catch (err) {
      console.error('Registration error:', err);
      setError('Failed to register student. See console for details.');
      setRegistrationCount(0);
      setTempDescriptors([]);
    }
  };

  const markTimeOut = async (studentName: string) => {
    const now = Date.now();
    if (now - actionLockRef.current < 1500) { 
        setError("Please wait a moment before marking out.");
        setTimeout(() => setError(null), 2000);
        return;
    }
    actionLockRef.current = now;

    if (!videoRef.current || !isCameraOn) {
      setError("Camera is not active. Please start the camera to mark out.");
      setTimeout(() => setError(null), 3000);
      return;
    }
    if (students.length === 0) {
      setError("No students registered. Cannot perform face recognition for mark out.");
      setTimeout(() => setError(null), 3000);
      return;
    }
    setError(`Verifying ${studentName} for mark out... Please look at the camera.`);
    const clearErrorTimeout = setTimeout(() => setError(null), 5000);
    try {
      const video = videoRef.current;
      const canvas = document.createElement('canvas');
      canvas.width = video.videoWidth || 640;
      canvas.height = video.videoHeight || 480;
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        clearTimeout(clearErrorTimeout);
        setError("Failed to process video frame for mark out.");
        setTimeout(() => setError(null), 3000);
        return;
      }
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
      const detection = await faceapi.detectSingleFace(canvas, new faceapi.SsdMobilenetv1Options({ minConfidence: 0.5 }))
        .withFaceLandmarks().withFaceDescriptor();
      clearTimeout(clearErrorTimeout);
      if (!detection) {
        setError(`No face detected. ${studentName} could not be marked out.`);
        setTimeout(() => setError(null), 3000);
        return;
      }
      const labeledDescriptors = students.map(student => new faceapi.LabeledFaceDescriptors(student.name, student.descriptors.map(d => new Float32Array(d))));
      const faceMatcher = new faceapi.FaceMatcher(labeledDescriptors, 0.5);
      const bestMatch = faceMatcher.findBestMatch(detection.descriptor);
      if (bestMatch.label === studentName) {
        const timeOutDate = new Date();
        const updatedAttendance = attendance.map(record => 
          record.student === studentName && !record.timeOut 
            ? { ...record, timeOut: timeOutDate } 
            : record
        );
        setAttendance(updatedAttendance);
        localStorage.setItem(LOCAL_STORAGE_ATTENDANCE_KEY, JSON.stringify(updatedAttendance));
        setError(`✅ ${studentName} marked out successfully at ${timeOutDate.toLocaleTimeString()}. Accuracy: ${((1 - bestMatch.distance) * 100).toFixed(2)}%`);
        setTimeout(() => setError(null), 3000);
      } else {
        if (bestMatch.label === 'unknown') {
          setError(`Face detected, but not recognized as ${studentName}. Mark out failed. Ensure good lighting and clear view.`);
        } else {
          setError(`Face recognized as ${bestMatch.label} (Accuracy: ${((1 - bestMatch.distance) * 100).toFixed(2)}%), not ${studentName}. Mark out failed.`);
        }
        setTimeout(() => setError(null), 4000);
      }
    } catch (err) {
      clearTimeout(clearErrorTimeout);
      console.error('Error during mark out face verification:', err);
      setError('An error occurred during face verification for mark out. Please try again.');
      setTimeout(() => setError(null), 3000);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h1 className="text-3xl font-bold text-center text-gray-800 mb-6">Temporary Face Attendance System</h1>
      
      {isLoading && (
        <div className="flex flex-col items-center justify-center p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
          <span className="text-gray-600">Loading face recognition models...</span>
        </div>
      )}
      
      {error && (
        <div className={`p-4 mb-6 text-sm rounded-lg ${error.startsWith('✅') ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`} role="alert">
          <span className="font-medium">{error.startsWith('✅') ? 'Success:' : 'Error:'}</span> {error.substring(error.startsWith('✅') ? 2 : (error.startsWith('Error:') ? 6 : 0) )}
           <button 
            onClick={() => setError(null)} 
            className="float-right font-bold text-lg -mt-1 -mr-1 px-2"
            aria-label="Close"
          >
            ×
          </button>
        </div>
      )}
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-xl font-semibold text-gray-700 mb-4">
            {registerMode ? 'Register New Student' : 'Attendance Camera'}
          </h2>
          <div className="relative w-full h-64 bg-gray-200 rounded-lg overflow-hidden mb-4 flex items-center justify-center">
            <video
              ref={videoRef}
              width={640}
              height={480}
              autoPlay
              muted
              playsInline
              className={`${isCameraOn ? 'block' : 'hidden'} w-full h-full object-cover`}
            />
            <canvas
              ref={canvasRef}
              width={640}
              height={480}
              className={`absolute top-0 left-0 w-full h-full ${isCameraOn && !registerMode ? 'block' : 'hidden'}`}
            />
            <canvas
              ref={registerPhotoRef}
              width={640} 
              height={480}
              className={`absolute top-0 left-0 w-full h-full ${registerMode && isCameraOn ? 'block' : 'hidden'}`}
            />
            {!isCameraOn && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-300">
                <p className="text-gray-500">Camera is off</p>
              </div>
            )}
            {recognizedFace && (
              <div className="absolute top-0 left-0 right-0 bg-green-500 text-white p-2 text-center font-bold animate-pulse text-md">
                ✅ {recognizedFace} ({(recognizedAccuracy! * 100).toFixed(1)}%)
              </div>
            )}
          </div>
          <div className="flex flex-wrap gap-2">
            {!isCameraOn ? (
              <button 
                onClick={handleStartCamera}
                disabled={isLoading || !modelsLoaded}
                className={`px-4 py-2 rounded-md text-white font-medium ${(isLoading || !modelsLoaded) ? 'bg-gray-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'}`}
              >
                Start Camera
              </button>
            ) : (
              <button 
                onClick={stopCamera}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-md"
              >
                Stop Camera
              </button>
            )}
            {isCameraOn && !registerMode && (
              <button
                onClick={handleMarkAttendanceClick}
                disabled={!modelsLoaded || students.length === 0}
                className={`px-4 py-2 text-white font-medium rounded-md ${(!modelsLoaded || students.length === 0) ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700'}`}
              >
                Mark Attendance (Face)
              </button>
            )}
            {isCameraOn && !registerMode && (
              <button
                onClick={() => {
                  console.log("Register New Student button clicked.");
                  setRegisterMode(true);
                }}
                className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-md"
              >
                Register New Student
              </button>
            )}
            {registerMode && (
              <button
                onClick={() => {
                  setRegisterMode(false);
                  setRegistrationCount(0); 
                  setTempDescriptors([]);
                  setError(null); 
                }}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-md"
              >
                Cancel Registration
              </button>
            )}
          </div>
          {registerMode && isCameraOn && (
            <div className="mt-4">
              <label className="block text-gray-700 mb-2">
                Student Name {registrationCount > 0 ? `(Capturing pose ${registrationCount + 1}/3)` : ''}
              </label>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newStudentName}
                  onChange={(e) => setNewStudentName(e.target.value)}
                  className="flex-1 p-2 border border-gray-300 rounded-md"
                  placeholder="Enter student name"
                  disabled={registrationCount >= 3}
                />
                <button
                  onClick={registerStudent}
                  className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white font-medium rounded-md"
                  disabled={!newStudentName.trim() || registrationCount >= 3}
                >
                  {registrationCount < 2 ? 'Capture Pose' : 'Register'}
                </button>
              </div>
            </div>
          )}
        </div>
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-xl font-semibold text-gray-700 mb-4">Attendance Records ({attendance.length})</h2>
          <div className="overflow-y-auto max-h-96">
            {attendance.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No attendance records yet</p>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Student</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Time In</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Time Out</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Action</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {attendance.sort((a,b) => b.timeIn.getTime() - a.timeIn.getTime()).map((record, index) => (
                    <tr key={index}>
                      <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{record.student}</td>
                      <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{record.timeIn.toLocaleTimeString()}</td>
                      <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{record.timeOut ? record.timeOut.toLocaleTimeString() : '-'}</td>
                      <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                        {!record.timeOut && (
                          <button
                            onClick={() => markTimeOut(record.student)}
                            className="text-xs px-2 py-1 bg-red-100 text-red-800 rounded hover:bg-red-200"
                          >
                            Mark Out
                          </button>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>
      </div>
      <div className="bg-gray-50 p-4 rounded-lg">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-gray-700">Registered Students ({students.length})</h2>
          <div className="flex gap-2">
            <button
              onClick={() => {
                if (window.confirm('Are you sure you want to clear all data? This will remove all students and attendance records.')) {
                  setStudents([]);
                  setAttendance([]);
                  localStorage.removeItem(LOCAL_STORAGE_STUDENTS_KEY);
                  localStorage.removeItem(LOCAL_STORAGE_ATTENDANCE_KEY);
                  setError("✅ All data cleared.");
                  setTimeout(() => setError(null), 2000);
                }
              }}
              className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm font-medium rounded-md"
            >
              Clear All Data
            </button>
          </div>
        </div>
        {students.length === 0 ? (
          <p className="text-gray-500 text-center py-4">No students registered yet</p>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {students.map((student, index) => (
              <div key={index} className="bg-white p-3 rounded-lg shadow-sm border border-gray-200">
                <img 
                  src={student.photo} 
                  alt={student.name}
                  className="w-full h-24 object-cover rounded mb-2"
                />
                <p className="text-sm font-medium text-center">{student.name}</p>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}