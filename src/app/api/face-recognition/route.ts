import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Dynamic initialization to avoid build issues
let faceapi: any;
let CanvasLib: any;
let ImageLib: any;
let ImageDataLib: any;
let initialized = false;

// Initialize face-api.js and canvas dynamically
const initializeFaceApi = async () => {
  if (initialized) return true;

  try {
    // Dynamic imports to avoid build-time issues
    faceapi = require('face-api.js');
    const canvas = require('canvas');
    const fetch = require('node-fetch');

    CanvasLib = canvas.Canvas;
    ImageLib = canvas.Image;
    ImageDataLib = canvas.ImageData;

    // Setup global environment for Node.js
    global.HTMLCanvasElement = CanvasLib;
    global.HTMLImageElement = ImageLib;
    global.ImageData = ImageDataLib;
    global.fetch = fetch;

    // Setup face-api environment using the proper API
    const { Canvas, Image, ImageData } = canvas;
    faceapi.env.monkeyPatch({ Canvas, Image, ImageData });

    initialized = true;
    console.log('Face-api.js environment initialized successfully');
    return true;
  } catch (error) {
    console.error('Error initializing face-api.js:', error);
    return false;
  }
};

let modelsLoaded = false;

async function loadModels() {
  if (modelsLoaded) return;

  // Initialize face-api.js first
  const isInitialized = await initializeFaceApi();
  if (!isInitialized) {
    throw new Error('Face-api.js environment not properly initialized');
  }

  const modelPath = path.join(process.cwd(), 'public/models');

  // Check if models directory exists
  if (!fs.existsSync(modelPath)) {
    throw new Error('Models directory not found. Please ensure face-api.js models are installed in public/models/');
  }

  // Check for required model files
  const requiredModels = [
    'tiny_face_detector_model-weights_manifest.json',
    'face_landmark_68_model-weights_manifest.json',
    'face_recognition_model-weights_manifest.json'
  ];

  console.log('Checking for required model files...');
  for (const modelFile of requiredModels) {
    const filePath = path.join(modelPath, modelFile);
    if (!fs.existsSync(filePath)) {
      throw new Error(`Required model file missing: ${modelFile}. Please download all face-api.js models.`);
    }
    console.log(`✓ Found: ${modelFile}`);
  }

  try {
    // Load models using the proper face-api.js API
    console.log('Loading face detection model...');
    await faceapi.nets.tinyFaceDetector.loadFromDisk(modelPath);

    console.log('Loading face landmark model...');
    await faceapi.nets.faceLandmark68Net.loadFromDisk(modelPath);

    console.log('Loading face recognition model...');
    await faceapi.nets.faceRecognitionNet.loadFromDisk(modelPath);

    modelsLoaded = true;
    console.log('✅ All face recognition models loaded successfully');
  } catch (error) {
    console.error('❌ Error loading face-api.js models:', error);
    throw new Error(`Failed to load face recognition models: ${error.message}`);
  }
}

async function loadReferenceImages() {
  const referenceDir = path.join(process.cwd(), 'public/reference-images');
  const labeledDescriptors = [];

  if (!fs.existsSync(referenceDir)) {
    console.warn('Reference images directory not found:', referenceDir);
    return [];
  }

  let files;
  try {
    files = fs.readdirSync(referenceDir);
  } catch (error) {
    console.error('Error reading reference directory:', error);
    throw new Error('Cannot access reference images directory');
  }

  const imageFiles = files.filter(file =>
    /\.(jpg|jpeg|png|gif)$/i.test(file)
  );

  if (imageFiles.length === 0) {
    console.warn('No image files found in reference directory');
    return [];
  }

  console.log(`Processing ${imageFiles.length} reference images...`);
  let processedCount = 0;
  let errorCount = 0;

  for (const file of imageFiles) {
    try {
      const imagePath = path.join(referenceDir, file);

      // Check if file exists and is readable
      if (!fs.existsSync(imagePath)) {
        console.warn(`Reference image not found: ${file}`);
        errorCount++;
        continue;
      }

      // Load image using canvas for Node.js
      const imageBuffer = fs.readFileSync(imagePath);
      const img = new ImageLib();
      img.src = imageBuffer;

      // Use the proper face-api.js high-level API
      const detection = await faceapi
        .detectSingleFace(img, new faceapi.TinyFaceDetectorOptions({ inputSize: 416, scoreThreshold: 0.5 }))
        .withFaceLandmarks()
        .withFaceDescriptor();

      if (detection && detection.descriptor) {
        // Create a labeled descriptor for this reference image
        // Use filename without extension as label
        const label = file.replace(/\.[^/.]+$/, "");

        labeledDescriptors.push({
          label: label,
          filename: file,
          descriptor: detection.descriptor,
        });
        processedCount++;
        console.log(`✅ Processed reference image: ${file}`);
      } else {
        console.warn(`❌ No face detected in reference image: ${file}`);
        errorCount++;
      }
    } catch (error) {
      console.error(`❌ Error processing reference image ${file}:`, error);
      errorCount++;
    }
  }

  console.log(`📊 Reference images processed: ${processedCount} successful, ${errorCount} failed`);

  return labeledDescriptors;
}

export async function POST(request: NextRequest) {
  try {
    // Load models with error handling
    try {
      await loadModels();
    } catch (modelError: any) {
      console.error('Model loading error:', modelError);
      return NextResponse.json({
        error: modelError.message || 'Failed to load face recognition models',
        errorType: 'PROCESSING'
      }, { status: 500 });
    }

    // Parse form data
    let formData;
    try {
      formData = await request.formData();
    } catch (error) {
      return NextResponse.json({
        error: 'Invalid form data',
        errorType: 'PROCESSING'
      }, { status: 400 });
    }

    const file = formData.get('image') as File;

    if (!file) {
      return NextResponse.json({
        error: 'No image provided',
        errorType: 'PROCESSING'
      }, { status: 400 });
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json({
        error: 'Invalid file type. Please upload an image file.',
        errorType: 'PROCESSING'
      }, { status: 400 });
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json({
        error: 'File too large. Maximum size is 10MB.',
        errorType: 'PROCESSING'
      }, { status: 400 });
    }

    // Convert file to buffer
    let bytes, buffer;
    try {
      bytes = await file.arrayBuffer();
      buffer = Buffer.from(bytes);
    } catch (error) {
      return NextResponse.json({
        error: 'Failed to process image file',
        errorType: 'PROCESSING'
      }, { status: 400 });
    }

    // Process image directly in memory (no need for temporary files)
    let detections;

    try {
      // Ensure face-api is initialized
      await initializeFaceApi();

      // Load image using canvas (for Node.js environment)
      const img = new ImageLib();
      img.src = buffer;

      // Detect ALL faces in uploaded image using proper face-api.js API (supports group photos)
      try {
        console.log('🔍 Starting face detection for group photo support...');

        detections = await faceapi
          .detectAllFaces(img, new faceapi.TinyFaceDetectorOptions({
            inputSize: 416,
            scoreThreshold: 0.5
          }))
          .withFaceLandmarks()
          .withFaceDescriptors();

        console.log(`✅ Face detection completed: ${detections ? detections.length : 0} face(s) found`);
      } catch (error) {
        console.error('❌ Face detection error:', error);
        return NextResponse.json({
          error: 'Failed to process image for face detection',
          errorType: 'PROCESSING'
        }, { status: 500 });
      }

      if (!detections || detections.length === 0) {
        return NextResponse.json({
          error: 'No faces detected in uploaded image. Please ensure faces are clearly visible and well-lit.',
          errorType: 'NO_FACE'
        }, { status: 400 });
      }

    } catch (imageProcessingError) {
      console.error('❌ Error processing uploaded image:', imageProcessingError);
      return NextResponse.json({
        error: 'Failed to process uploaded image',
        errorType: 'PROCESSING'
      }, { status: 500 });
    }
    
    // Load reference images
    let referenceDescriptors;
    try {
      referenceDescriptors = await loadReferenceImages();
    } catch (error) {
      console.error('Reference images loading error:', error);
      return NextResponse.json({
        error: 'Failed to load reference images',
        errorType: 'NO_REFERENCE'
      }, { status: 500 });
    }

    if (referenceDescriptors.length === 0) {
      return NextResponse.json({
        error: 'No reference images found. Please add images to the public/reference-images folder.',
        errorType: 'NO_REFERENCE'
      }, { status: 400 });
    }
    
    console.log(`📊 Processing ${detections.length} detected face(s) against ${referenceDescriptors.length} reference images...`);

    // Create labeled face descriptors for FaceMatcher
    const labeledFaceDescriptors = referenceDescriptors.map(ref =>
      new faceapi.LabeledFaceDescriptors(ref.label, [ref.descriptor])
    );

    // Create FaceMatcher with reference descriptors
    const faceMatcher = new faceapi.FaceMatcher(labeledFaceDescriptors, 0.6); // 0.6 is a good threshold for face matching

    const allMatches = [];
    const detectedFaces = [];

    // Process each detected face
    for (let faceIndex = 0; faceIndex < detections.length; faceIndex++) {
      const detection = detections[faceIndex];

      console.log(`🔍 Processing face ${faceIndex + 1}/${detections.length}...`);

      try {
        // Use FaceMatcher to find the best match for this face
        const bestMatch = faceMatcher.findBestMatch(detection.descriptor);

        const boundingBox = {
          x: Math.round(detection.detection.box.x),
          y: Math.round(detection.detection.box.y),
          width: Math.round(detection.detection.box.width),
          height: Math.round(detection.detection.box.height)
        };

        const faceMatches = [];
        let matchResult = null;

        // Check if it's a valid match (not "unknown")
        if (bestMatch.label !== 'unknown') {
          // Find the corresponding reference descriptor to get filename
          const refDescriptor = referenceDescriptors.find(ref => ref.label === bestMatch.label);

          if (refDescriptor) {
            const confidence = Math.round((1 - bestMatch.distance) * 100 * 100) / 100; // Convert to percentage

            matchResult = {
              filename: refDescriptor.filename,
              label: bestMatch.label,
              confidence: confidence,
              distance: Math.round(bestMatch.distance * 1000) / 1000,
              faceIndex: faceIndex,
              boundingBox: boundingBox
            };

            faceMatches.push(matchResult);
            allMatches.push(matchResult);

            console.log(`✅ Face ${faceIndex + 1} matched: ${refDescriptor.filename} (${confidence}% confidence)`);
          }
        } else {
          console.log(`❌ Face ${faceIndex + 1}: No match found (distance: ${bestMatch.distance.toFixed(3)})`);
        }

        // Add face info to detected faces array
        detectedFaces.push({
          faceIndex: faceIndex,
          boundingBox: boundingBox,
          matchCount: faceMatches.length,
          bestMatch: matchResult
        });

      } catch (error) {
        console.error(`❌ Error processing face ${faceIndex + 1}:`, error);

        // Add face info even if processing failed
        detectedFaces.push({
          faceIndex: faceIndex,
          boundingBox: {
            x: Math.round(detection.detection.box.x),
            y: Math.round(detection.detection.box.y),
            width: Math.round(detection.detection.box.width),
            height: Math.round(detection.detection.box.height)
          },
          matchCount: 0,
          bestMatch: null
        });
      }
    }

    // Sort all matches by confidence (highest first)
    allMatches.sort((a, b) => b.confidence - a.confidence);

    console.log(`🎯 Found ${allMatches.length} total matches from ${detections.length} detected face(s)`);

    return NextResponse.json({
      success: true,
      totalReferenceImages: referenceDescriptors.length,
      detectedFaces: detectedFaces,
      detectedFaceCount: detections.length,
      matches: allMatches,
      matchCount: allMatches.length,
    });
    
  } catch (error: unknown) {
    console.error('❌ Face recognition error:', error);

    let errorMessage = 'Face recognition failed';
    let errorType = 'UNKNOWN';

    if (error instanceof Error && error.message) {
      if (error.message.includes('model')) {
        errorMessage = 'Face recognition models not available';
        errorType = 'PROCESSING';
      } else if (error.message.includes('face')) {
        errorMessage = 'Face detection failed';
        errorType = 'NO_FACE';
      } else if (error.message.includes('reference')) {
        errorMessage = 'Reference images not available';
        errorType = 'NO_REFERENCE';
      } else {
        errorMessage = error.message;
        errorType = 'PROCESSING';
      }
    }

    return NextResponse.json({
      error: errorMessage,
      errorType: errorType
    }, { status: 500 });
  }
}
