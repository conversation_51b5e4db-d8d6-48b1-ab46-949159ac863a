import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Dynamic initialization to avoid build issues
let faceapi: any;
let CanvasLib: any;
let ImageLib: any;
let ImageDataLib: any;
let initialized = false;

// Initialize face-api.js and canvas dynamically
const initializeFaceApi = async () => {
  if (initialized) return true;

  try {
    // Dynamic imports to avoid build-time issues
    faceapi = require('face-api.js');
    const canvas = require('canvas');
    const fetch = require('node-fetch');

    CanvasLib = canvas.Canvas;
    ImageLib = canvas.Image;
    ImageDataLib = canvas.ImageData;

    // Setup global environment for Node.js
    global.HTMLCanvasElement = CanvasLib;
    global.HTMLImageElement = ImageLib;
    global.ImageData = ImageDataLib;
    global.fetch = fetch;

    // Setup face-api environment using the proper API
    const { Canvas, Image, ImageData } = canvas;
    faceapi.env.monkeyPatch({ Canvas, Image, ImageData });

    initialized = true;
    console.log('Face-api.js environment initialized successfully');
    return true;
  } catch (error) {
    console.error('Error initializing face-api.js:', error);
    return false;
  }
};

let modelsLoaded = false;
let referenceDescriptorsCache: any[] = [];
let referenceLastModified = 0;

async function loadModels() {
  if (modelsLoaded) return;

  // Initialize face-api.js first
  const isInitialized = await initializeFaceApi();
  if (!isInitialized) {
    throw new Error('Face-api.js environment not properly initialized');
  }

  const modelPath = path.join(process.cwd(), 'public/models');

  // Check if models directory exists
  if (!fs.existsSync(modelPath)) {
    throw new Error('Models directory not found. Please ensure face-api.js models are installed in public/models/');
  }

  // Check for required model files
  const requiredModels = [
    'tiny_face_detector_model-weights_manifest.json',
    'face_landmark_68_model-weights_manifest.json',
    'face_recognition_model-weights_manifest.json'
  ];

  console.log('Checking for required model files...');
  for (const modelFile of requiredModels) {
    const filePath = path.join(modelPath, modelFile);
    if (!fs.existsSync(filePath)) {
      throw new Error(`Required model file missing: ${modelFile}. Please download all face-api.js models.`);
    }
    console.log(`✓ Found: ${modelFile}`);
  }

  try {
    // Load models using the proper face-api.js API
    console.log('Loading face detection model...');
    await faceapi.nets.tinyFaceDetector.loadFromDisk(modelPath);

    console.log('Loading face landmark model...');
    await faceapi.nets.faceLandmark68Net.loadFromDisk(modelPath);

    console.log('Loading face recognition model...');
    await faceapi.nets.faceRecognitionNet.loadFromDisk(modelPath);

    modelsLoaded = true;
    console.log('✅ All face recognition models loaded successfully');
  } catch (error) {
    console.error('❌ Error loading face-api.js models:', error);
    throw new Error(`Failed to load face recognition models: ${error.message}`);
  }
}

async function loadReferenceImages() {
  const referenceDir = path.join(process.cwd(), 'public/reference-images');

  if (!fs.existsSync(referenceDir)) {
    console.warn('Reference images directory not found:', referenceDir);
    return [];
  }

  // Check if we can use cached results
  try {
    const dirStats = fs.statSync(referenceDir);
    const currentModified = dirStats.mtime.getTime();

    if (referenceDescriptorsCache.length > 0 && currentModified <= referenceLastModified) {
      console.log(`📋 Using cached reference descriptors (${referenceDescriptorsCache.length} faces)`);
      return referenceDescriptorsCache;
    }

    // Update last modified time
    referenceLastModified = currentModified;
  } catch (error) {
    console.warn('Could not check directory modification time:', error);
  }

  const labeledDescriptors = [];

  let files;
  try {
    files = fs.readdirSync(referenceDir);
  } catch (error) {
    console.error('Error reading reference directory:', error);
    throw new Error('Cannot access reference images directory');
  }

  const imageFiles = files.filter(file =>
    /\.(jpg|jpeg|png|gif|heic|heif)$/i.test(file)
  );

  if (imageFiles.length === 0) {
    console.warn('No image files found in reference directory');
    return [];
  }

  console.log(`🔍 Processing ${imageFiles.length} reference images (supporting both single faces and group photos)...`);
  let processedFaceCount = 0;
  let processedImageCount = 0;
  let errorCount = 0;

  for (const file of imageFiles) {
    try {
      const imagePath = path.join(referenceDir, file);

      // Check if file exists and is readable
      if (!fs.existsSync(imagePath)) {
        console.warn(`Reference image not found: ${file}`);
        errorCount++;
        continue;
      }

      // Load image using canvas for Node.js
      const imageBuffer = fs.readFileSync(imagePath);
      const img = new ImageLib();
      img.src = imageBuffer;

      // Use detectAllFaces to handle both single faces and group photos in reference images
      const detections = await faceapi
        .detectAllFaces(img, new faceapi.TinyFaceDetectorOptions({ inputSize: 416, scoreThreshold: 0.5 }))
        .withFaceLandmarks()
        .withFaceDescriptors();

      if (detections && detections.length > 0) {
        console.log(`📸 Found ${detections.length} face(s) in reference image: ${file}`);

        // Process each face found in this reference image
        for (let faceIndex = 0; faceIndex < detections.length; faceIndex++) {
          const detection = detections[faceIndex];

          if (detection.descriptor) {
            // Create unique label for each face in the image
            const baseLabel = file.replace(/\.[^/.]+$/, "");
            const label = detections.length > 1 ? `${baseLabel}_face${faceIndex + 1}` : baseLabel;

            labeledDescriptors.push({
              label: label,
              filename: file,
              faceIndex: faceIndex,
              totalFacesInImage: detections.length,
              descriptor: detection.descriptor,
              boundingBox: {
                x: Math.round(detection.detection.box.x),
                y: Math.round(detection.detection.box.y),
                width: Math.round(detection.detection.box.width),
                height: Math.round(detection.detection.box.height)
              }
            });
            processedFaceCount++;
            console.log(`  ✅ Face ${faceIndex + 1}/${detections.length}: ${label}`);
          }
        }
        processedImageCount++;
      } else {
        console.warn(`❌ No faces detected in reference image: ${file}`);
        errorCount++;
      }
    } catch (error) {
      console.error(`❌ Error processing reference image ${file}:`, error);
      errorCount++;
    }
  }

  console.log(`📊 Reference processing complete:`);
  console.log(`  📁 Images processed: ${processedImageCount} successful, ${errorCount} failed`);
  console.log(`  👥 Total faces extracted: ${processedFaceCount} faces from ${processedImageCount} images`);
  console.log(`  🎯 Average faces per image: ${(processedFaceCount / Math.max(processedImageCount, 1)).toFixed(1)}`);

  // Update cache
  referenceDescriptorsCache = [...labeledDescriptors];
  console.log(`💾 Reference descriptors cached for future requests`);

  return labeledDescriptors;
}

export async function POST(request: NextRequest) {
  try {
    // Load models with error handling
    try {
      await loadModels();
    } catch (modelError: any) {
      console.error('Model loading error:', modelError);
      return NextResponse.json({
        error: modelError.message || 'Failed to load face recognition models',
        errorType: 'PROCESSING'
      }, { status: 500 });
    }

    // Parse form data
    let formData;
    try {
      formData = await request.formData();
    } catch (error) {
      return NextResponse.json({
        error: 'Invalid form data',
        errorType: 'PROCESSING'
      }, { status: 400 });
    }

    const file = formData.get('image') as File;

    if (!file) {
      return NextResponse.json({
        error: 'No image provided',
        errorType: 'PROCESSING'
      }, { status: 400 });
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json({
        error: 'Invalid file type. Please upload an image file.',
        errorType: 'PROCESSING'
      }, { status: 400 });
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json({
        error: 'File too large. Maximum size is 10MB.',
        errorType: 'PROCESSING'
      }, { status: 400 });
    }

    // Convert file to buffer
    let bytes, buffer;
    try {
      bytes = await file.arrayBuffer();
      buffer = Buffer.from(bytes);
    } catch (error) {
      return NextResponse.json({
        error: 'Failed to process image file',
        errorType: 'PROCESSING'
      }, { status: 400 });
    }

    // Process image directly in memory (no need for temporary files)
    let detections;

    try {
      // Ensure face-api is initialized
      await initializeFaceApi();

      // Load image using canvas (for Node.js environment)
      const img = new ImageLib();
      img.src = buffer;

      // Detect ALL faces in uploaded image using proper face-api.js API (supports group photos)
      try {
        console.log('🔍 Starting face detection for group photo support...');

        detections = await faceapi
          .detectAllFaces(img, new faceapi.TinyFaceDetectorOptions({
            inputSize: 416,
            scoreThreshold: 0.5
          }))
          .withFaceLandmarks()
          .withFaceDescriptors();

        console.log(`✅ Face detection completed: ${detections ? detections.length : 0} face(s) found`);
      } catch (error) {
        console.error('❌ Face detection error:', error);
        return NextResponse.json({
          error: 'Failed to process image for face detection',
          errorType: 'PROCESSING'
        }, { status: 500 });
      }

      if (!detections || detections.length === 0) {
        return NextResponse.json({
          error: 'No faces detected in uploaded image. Please ensure faces are clearly visible and well-lit.',
          errorType: 'NO_FACE'
        }, { status: 400 });
      }

      // Validate face count - maximum 2 faces allowed
      if (detections.length > 2) {
        console.log(`❌ Too many faces detected: ${detections.length} faces found (maximum 2 allowed)`);
        return NextResponse.json({
          error: `Only 1 or 2 faces are allowed — more than that cannot be used for matching. Found ${detections.length} faces in your image.`,
          errorType: 'TOO_MANY_FACES',
          detectedFaceCount: detections.length
        }, { status: 400 });
      }

      console.log(`✅ Face count validation passed: ${detections.length} face(s) detected (within limit)`);

    } catch (imageProcessingError) {
      console.error('❌ Error processing uploaded image:', imageProcessingError);
      return NextResponse.json({
        error: 'Failed to process uploaded image',
        errorType: 'PROCESSING'
      }, { status: 500 });
    }
    
    // Load reference images
    let referenceDescriptors;
    try {
      referenceDescriptors = await loadReferenceImages();
    } catch (error) {
      console.error('Reference images loading error:', error);
      return NextResponse.json({
        error: 'Failed to load reference images',
        errorType: 'NO_REFERENCE'
      }, { status: 500 });
    }

    if (referenceDescriptors.length === 0) {
      return NextResponse.json({
        error: 'No reference images found. Please add images to the public/reference-images folder.',
        errorType: 'NO_REFERENCE'
      }, { status: 400 });
    }
    
    console.log(`📊 Processing ${detections.length} detected face(s) against ${referenceDescriptors.length} reference images...`);

    const allMatches = [];
    const detectedFaces = [];
    const confidenceThreshold = 50; // Minimum confidence percentage to display results
    const distanceThreshold = 0.6; // Maximum distance for face matching

    // Process each detected face
    for (let faceIndex = 0; faceIndex < detections.length; faceIndex++) {
      const detection = detections[faceIndex];

      console.log(`🔍 Processing face ${faceIndex + 1}/${detections.length}...`);

      try {
        const boundingBox = {
          x: Math.round(detection.detection.box.x),
          y: Math.round(detection.detection.box.y),
          width: Math.round(detection.detection.box.width),
          height: Math.round(detection.detection.box.height)
        };

        const faceMatches = [];

        // Compare this face against ALL reference faces (from both single and group photos)
        console.log(`  🔍 Comparing face ${faceIndex + 1} against ${referenceDescriptors.length} reference faces...`);

        for (const ref of referenceDescriptors) {
          try {
            // Calculate distance between face descriptors
            const distance = faceapi.euclideanDistance(detection.descriptor, ref.descriptor);
            const confidence = Math.round((1 - distance) * 100 * 100) / 100; // Convert to percentage

            // Create display name for reference face
            const refDisplayName = ref.totalFacesInImage > 1
              ? `${ref.filename} (Face ${ref.faceIndex + 1}/${ref.totalFacesInImage})`
              : ref.filename;

            console.log(`    📏 ${refDisplayName}: distance=${distance.toFixed(3)}, confidence=${confidence}%`);

            // Include match if distance is below threshold AND confidence is above minimum
            if (distance < distanceThreshold && confidence >= confidenceThreshold) {
              const matchResult = {
                filename: ref.filename,
                label: ref.label,
                confidence: confidence,
                distance: Math.round(distance * 1000) / 1000,
                faceIndex: faceIndex,
                boundingBox: boundingBox,
                // Additional info for reference face
                referenceFaceIndex: ref.faceIndex,
                referenceTotalFaces: ref.totalFacesInImage,
                referenceBoundingBox: ref.boundingBox
              };

              faceMatches.push(matchResult);
              allMatches.push(matchResult);

              console.log(`    ✅ MATCH: ${refDisplayName} (${confidence}% confidence, distance: ${distance.toFixed(3)})`);
            } else {
              const reason = confidence < confidenceThreshold ? `confidence ${confidence}% < ${confidenceThreshold}%` : `distance ${distance.toFixed(3)} >= ${distanceThreshold}`;
              console.log(`    ❌ NO MATCH: ${refDisplayName} (${reason})`);
            }
          } catch (error) {
            console.error(`    ❌ Error comparing with ${ref.filename}:`, error);
          }
        }

        // Sort matches for this face by confidence (highest first)
        faceMatches.sort((a, b) => b.confidence - a.confidence);

        // Add face info to detected faces array
        detectedFaces.push({
          faceIndex: faceIndex,
          boundingBox: boundingBox,
          matchCount: faceMatches.length,
          bestMatch: faceMatches.length > 0 ? faceMatches[0] : null
        });

        console.log(`🎯 Face ${faceIndex + 1} results: ${faceMatches.length} matches found`);

      } catch (error) {
        console.error(`❌ Error processing face ${faceIndex + 1}:`, error);

        // Add face info even if processing failed
        detectedFaces.push({
          faceIndex: faceIndex,
          boundingBox: {
            x: Math.round(detection.detection.box.x),
            y: Math.round(detection.detection.box.y),
            width: Math.round(detection.detection.box.width),
            height: Math.round(detection.detection.box.height)
          },
          matchCount: 0,
          bestMatch: null
        });
      }
    }

    // Sort all matches by confidence (highest first)
    allMatches.sort((a, b) => b.confidence - a.confidence);

    console.log(`🎯 Found ${allMatches.length} total matches from ${detections.length} detected face(s) (minimum ${confidenceThreshold}% confidence)`);
    console.log(`📋 Match summary:`);
    allMatches.forEach((match, index) => {
      console.log(`  ${index + 1}. ${match.filename} - Face ${match.faceIndex + 1} (${match.confidence}%)`);
    });

    return NextResponse.json({
      success: true,
      totalReferenceImages: referenceDescriptors.length,
      detectedFaces: detectedFaces,
      detectedFaceCount: detections.length,
      matches: allMatches,
      matchCount: allMatches.length,
      confidenceThreshold: confidenceThreshold,
    });
    
  } catch (error: unknown) {
    console.error('❌ Face recognition error:', error);

    let errorMessage = 'Face recognition failed';
    let errorType = 'UNKNOWN';

    if (error instanceof Error && error.message) {
      if (error.message.includes('model')) {
        errorMessage = 'Face recognition models not available';
        errorType = 'PROCESSING';
      } else if (error.message.includes('face')) {
        errorMessage = 'Face detection failed';
        errorType = 'NO_FACE';
      } else if (error.message.includes('reference')) {
        errorMessage = 'Reference images not available';
        errorType = 'NO_REFERENCE';
      } else {
        errorMessage = error.message;
        errorType = 'PROCESSING';
      }
    }

    return NextResponse.json({
      error: errorMessage,
      errorType: errorType
    }, { status: 500 });
  }
}
