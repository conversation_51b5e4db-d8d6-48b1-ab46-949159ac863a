import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

// Dynamic initialization to avoid build issues
let faceapi: any;
let CanvasLib: any;
let ImageLib: any;
let ImageDataLib: any;
let initialized = false;

// Initialize face-api.js and canvas dynamically
const initializeFaceApi = async () => {
  if (initialized) return true;

  try {
    // Dynamic imports to avoid build-time issues
    faceapi = require('face-api.js');
    const canvas = require('canvas');
    const fetch = require('node-fetch');

    CanvasLib = canvas.Canvas;
    ImageLib = canvas.Image;
    ImageDataLib = canvas.ImageData;

    // Setup global environment
    global.HTMLCanvasElement = CanvasLib;
    global.HTMLImageElement = ImageLib;
    global.ImageData = ImageDataLib;
    global.fetch = fetch;

    // Setup face-api environment
    faceapi.env.monkeyPatch({
      Canvas: CanvasLib,
      Image: ImageLib,
      ImageData: ImageDataLib,
      createCanvasElement: () => new CanvasLib(1, 1),
      createImageElement: () => new ImageLib()
    });

    initialized = true;
    return true;
  } catch (error) {
    console.error('Error initializing face-api.js:', error);
    return false;
  }
};

let modelsLoaded = false;

async function loadModels() {
  if (modelsLoaded) return;

  // Initialize face-api.js first
  const isInitialized = await initializeFaceApi();
  if (!isInitialized) {
    throw new Error('Face-api.js environment not properly initialized');
  }

  const modelPath = path.join(process.cwd(), 'public/models');

  // Check if models directory exists
  if (!fs.existsSync(modelPath)) {
    throw new Error('Models directory not found. Please ensure face-api.js models are installed in public/models/');
  }

  // Check for required model files
  const requiredModels = [
    'tiny_face_detector_model-weights_manifest.json',
    'face_landmark_68_model-weights_manifest.json',
    'face_recognition_model-weights_manifest.json'
  ];

  console.log('Checking for required model files...');
  for (const modelFile of requiredModels) {
    const filePath = path.join(modelPath, modelFile);
    if (!fs.existsSync(filePath)) {
      throw new Error(`Required model file missing: ${modelFile}. Please download all face-api.js models.`);
    }
    console.log(`✓ Found: ${modelFile}`);
  }

  try {
    // @vladmandic/face-api uses nets API similar to original face-api.js
    await Promise.all([
      faceapi.nets.tinyFaceDetector.loadFromDisk(modelPath),
      faceapi.nets.faceLandmark68Net.loadFromDisk(modelPath),
      faceapi.nets.faceRecognitionNet.loadFromDisk(modelPath),
    ]);

    modelsLoaded = true;
    console.log('Face recognition models loaded successfully');
  } catch (error) {
    console.error('Error loading @vladmandic/face-api models:', error);
    throw new Error('Failed to load face recognition models. Please check model files.');
  }
}

async function loadReferenceImages() {
  const referenceDir = path.join(process.cwd(), 'public/reference-images');
  const referenceDescriptors = [];

  if (!fs.existsSync(referenceDir)) {
    console.warn('Reference images directory not found:', referenceDir);
    return [];
  }

  let files;
  try {
    files = fs.readdirSync(referenceDir);
  } catch (error) {
    console.error('Error reading reference directory:', error);
    throw new Error('Cannot access reference images directory');
  }

  const imageFiles = files.filter(file =>
    /\.(jpg|jpeg|png|gif)$/i.test(file)
  );

  if (imageFiles.length === 0) {
    console.warn('No image files found in reference directory');
    return [];
  }

  let processedCount = 0;
  let errorCount = 0;

  for (const file of imageFiles) {
    try {
      const imagePath = path.join(referenceDir, file);

      // Check if file exists and is readable
      if (!fs.existsSync(imagePath)) {
        console.warn(`Reference image not found: ${file}`);
        errorCount++;
        continue;
      }

      // Load image using canvas for Node.js
      const imageBuffer = fs.readFileSync(imagePath);
      const img = new ImageLib();
      img.src = imageBuffer;

      const detection = await faceapi
        .detectSingleFace(img, new faceapi.TinyFaceDetectorOptions({ inputSize: 416, scoreThreshold: 0.5 }))
        .withFaceLandmarks()
        .withFaceDescriptor();

      if (detection) {
        referenceDescriptors.push({
          filename: file,
          descriptor: detection.descriptor,
        });
        processedCount++;
      } else {
        console.warn(`No face detected in reference image: ${file}`);
        errorCount++;
      }
    } catch (error) {
      console.error(`Error processing reference image ${file}:`, error);
      errorCount++;
    }
  }

  console.log(`Reference images processed: ${processedCount} successful, ${errorCount} failed`);

  return referenceDescriptors;
}

export async function POST(request: NextRequest) {
  try {
    // Load models with error handling
    try {
      await loadModels();
    } catch (modelError: any) {
      console.error('Model loading error:', modelError);
      return NextResponse.json({
        error: modelError.message || 'Failed to load face recognition models',
        errorType: 'PROCESSING'
      }, { status: 500 });
    }

    // Parse form data
    let formData;
    try {
      formData = await request.formData();
    } catch (error) {
      return NextResponse.json({
        error: 'Invalid form data',
        errorType: 'PROCESSING'
      }, { status: 400 });
    }

    const file = formData.get('image') as File;

    if (!file) {
      return NextResponse.json({
        error: 'No image provided',
        errorType: 'PROCESSING'
      }, { status: 400 });
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json({
        error: 'Invalid file type. Please upload an image file.',
        errorType: 'PROCESSING'
      }, { status: 400 });
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json({
        error: 'File too large. Maximum size is 10MB.',
        errorType: 'PROCESSING'
      }, { status: 400 });
    }

    // Convert file to buffer
    let bytes, buffer;
    try {
      bytes = await file.arrayBuffer();
      buffer = Buffer.from(bytes);
    } catch (error) {
      return NextResponse.json({
        error: 'Failed to process image file',
        errorType: 'PROCESSING'
      }, { status: 400 });
    }

    // Create temporary file for face-api.js processing
    const tempDir = path.join(process.cwd(), 'temp');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    const tempFilePath = path.join(tempDir, `temp-${Date.now()}-${Math.random().toString(36).substring(2, 11)}.jpg`);
    let detections;

    try {
      // Ensure face-api is initialized
      await initializeFaceApi();

      // Write buffer to temporary file
      fs.writeFileSync(tempFilePath, buffer);

      // Load image using canvas (for Node.js environment)
      const img = new ImageLib();
      img.src = buffer;

      // Detect ALL faces in uploaded image using @vladmandic/face-api (supports group photos)
      try {
        console.log('Starting face detection for group photo support...');
        detections = await faceapi
          .detectAllFaces(img, new faceapi.TinyFaceDetectorOptions({ inputSize: 416, scoreThreshold: 0.5 }))
          .withFaceLandmarks()
          .withFaceDescriptors();

        console.log('Face detection completed:', detections ? `${detections.length} face(s) found` : 'No faces detected');
      } catch (error) {
        console.error('Face detection error:', error);
        return NextResponse.json({
          error: 'Failed to process image for face detection',
          errorType: 'PROCESSING'
        }, { status: 500 });
      }

      if (!detections || detections.length === 0) {
        return NextResponse.json({
          error: 'No faces detected in uploaded image. Please ensure faces are clearly visible and well-lit.',
          errorType: 'NO_FACE'
        }, { status: 400 });
      }

    } catch (tempFileError) {
      console.error('Error with temporary file processing:', tempFileError);
      return NextResponse.json({
        error: 'Failed to process uploaded image',
        errorType: 'PROCESSING'
      }, { status: 500 });
    } finally {
      // Clean up temporary file
      try {
        if (fs.existsSync(tempFilePath)) {
          fs.unlinkSync(tempFilePath);
        }
      } catch (cleanupError) {
        console.error('Error cleaning up temp file:', cleanupError);
      }
    }
    
    // Load reference images
    let referenceDescriptors;
    try {
      referenceDescriptors = await loadReferenceImages();
    } catch (error) {
      console.error('Reference images loading error:', error);
      return NextResponse.json({
        error: 'Failed to load reference images',
        errorType: 'NO_REFERENCE'
      }, { status: 500 });
    }

    if (referenceDescriptors.length === 0) {
      return NextResponse.json({
        error: 'No reference images found. Please add images to the public/reference-images folder.',
        errorType: 'NO_REFERENCE'
      }, { status: 400 });
    }
    
    // Compare each detected face with all reference images and find matches
    const allMatches = [];
    const detectedFaces = [];
    const threshold = 0.7; // Adjusted threshold for better matching (lower = more strict)
    const minConfidence = 40; // Minimum confidence percentage to consider as match

    console.log(`Processing ${detections.length} detected face(s) against ${referenceDescriptors.length} reference images...`);

    // Process each detected face
    for (let faceIndex = 0; faceIndex < detections.length; faceIndex++) {
      const detection = detections[faceIndex];
      const faceMatches = [];

      console.log(`Processing face ${faceIndex + 1}/${detections.length}...`);

      // Compare this face against all reference images
      for (const ref of referenceDescriptors) {
        try {
          const distance = faceapi.euclideanDistance(detection.descriptor, ref.descriptor);
          const confidence = Math.max(0, Math.min(100, (1 - distance) * 100));

          // Include match if distance is below threshold AND confidence is above minimum
          if (distance < threshold && confidence >= minConfidence) {
            faceMatches.push({
              filename: ref.filename,
              confidence: Math.round(confidence * 100) / 100, // Round to 2 decimal places
              distance: Math.round(distance * 1000) / 1000, // Round to 3 decimal places
              faceIndex: faceIndex, // Track which face this match belongs to
              boundingBox: {
                x: Math.round(detection.detection.box.x),
                y: Math.round(detection.detection.box.y),
                width: Math.round(detection.detection.box.width),
                height: Math.round(detection.detection.box.height)
              }
            });
          }
        } catch (error) {
          console.error(`Error comparing face ${faceIndex + 1} with ${ref.filename}:`, error);
        }
      }

      // Sort matches for this face by confidence (highest first), then by distance (lowest first)
      faceMatches.sort((a, b) => {
        if (b.confidence !== a.confidence) {
          return b.confidence - a.confidence;
        }
        return a.distance - b.distance;
      });

      // Add face info to detected faces array
      detectedFaces.push({
        faceIndex: faceIndex,
        boundingBox: {
          x: Math.round(detection.detection.box.x),
          y: Math.round(detection.detection.box.y),
          width: Math.round(detection.detection.box.width),
          height: Math.round(detection.detection.box.height)
        },
        matchCount: faceMatches.length,
        bestMatch: faceMatches.length > 0 ? faceMatches[0] : null
      });

      // Add all matches from this face to the overall matches array
      allMatches.push(...faceMatches);
    }

    // Sort all matches by confidence (highest first), then by distance (lowest first)
    allMatches.sort((a, b) => {
      if (b.confidence !== a.confidence) {
        return b.confidence - a.confidence;
      }
      return a.distance - b.distance;
    });

    console.log(`Found ${allMatches.length} total matches from ${detections.length} detected face(s)`);

    return NextResponse.json({
      success: true,
      totalReferenceImages: referenceDescriptors.length,
      detectedFaces: detectedFaces,
      detectedFaceCount: detections.length,
      matches: allMatches,
      matchCount: allMatches.length,
    });
    
  } catch (error: any) {
    console.error('Face recognition error:', error);

    let errorMessage = 'Face recognition failed';
    let errorType = 'UNKNOWN';

    if (error.message) {
      if (error.message.includes('model')) {
        errorMessage = 'Face recognition models not available';
        errorType = 'PROCESSING';
      } else if (error.message.includes('face')) {
        errorMessage = 'Face detection failed';
        errorType = 'NO_FACE';
      } else if (error.message.includes('reference')) {
        errorMessage = 'Reference images not available';
        errorType = 'NO_REFERENCE';
      } else {
        errorMessage = error.message;
        errorType = 'PROCESSING';
      }
    }

    return NextResponse.json({
      error: errorMessage,
      errorType: errorType
    }, { status: 500 });
  }
}
