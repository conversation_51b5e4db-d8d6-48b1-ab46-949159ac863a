import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET() {
  try {
    // Test 1: Check if models directory exists
    const modelPath = path.join(process.cwd(), 'public/models');
    const modelsExist = fs.existsSync(modelPath);
    
    let modelFiles = [];
    if (modelsExist) {
      modelFiles = fs.readdirSync(modelPath);
    }
    
    // Test 2: Check if reference images directory exists
    const refPath = path.join(process.cwd(), 'public/reference-images');
    const refExist = fs.existsSync(refPath);
    
    let refFiles = [];
    if (refExist) {
      refFiles = fs.readdirSync(refPath);
    }
    
    // Test 3: Try to import face-api.js
    let faceApiStatus = 'not loaded';
    let faceApiError = null;
    
    try {
      const faceapi = require('face-api.js');
      faceApiStatus = 'loaded successfully';
    } catch (error: any) {
      faceApiStatus = 'failed to load';
      faceApiError = error.message;
    }
    
    // Test 4: Try to import canvas
    let canvasStatus = 'not loaded';
    let canvasError = null;

    try {
      const canvas = require('canvas');
      canvasStatus = 'loaded successfully';
    } catch (error: any) {
      canvasStatus = 'failed to load';
      canvasError = error.message;
    }

    // Test 5: Try to setup face-api.js environment
    let envSetupStatus = 'not attempted';
    let envSetupError = null;

    try {
      const faceapi = require('face-api.js');
      const canvas = require('canvas');
      const { Canvas, Image, ImageData } = canvas;

      // Set global variables
      if (typeof global !== 'undefined') {
        global.HTMLCanvasElement = Canvas;
        global.HTMLImageElement = Image;
        global.ImageData = ImageData;
        global.fetch = global.fetch || require('node-fetch');
      }

      // Force environment detection
      const env = faceapi.env;

      // Override environment detection methods
      env.isNodejs = function() { return true; };
      env.isBrowser = function() { return false; };
      env.getEnv = function() { return 'nodejs'; };

      // Apply polyfill
      env.monkeyPatch({ Canvas, Image, ImageData });

      envSetupStatus = 'success';
    } catch (error: any) {
      envSetupStatus = 'failed';
      envSetupError = error.message;
    }

    // Test 6: Try to load models
    let modelLoadStatus = 'not attempted';
    let modelLoadError = null;

    try {
      const faceapi = require('face-api.js');
      const canvas = require('canvas');
      const { Canvas, Image, ImageData } = canvas;

      // Set global variables
      if (typeof global !== 'undefined') {
        global.HTMLCanvasElement = Canvas;
        global.HTMLImageElement = Image;
        global.ImageData = ImageData;
        global.fetch = global.fetch || require('node-fetch');
      }

      // Force environment detection
      const env = faceapi.env;

      // Override environment detection methods
      env.isNodejs = function() { return true; };
      env.isBrowser = function() { return false; };
      env.getEnv = function() { return 'nodejs'; };

      // Apply polyfill
      env.monkeyPatch({ Canvas, Image, ImageData });

      await Promise.all([
        faceapi.nets.tinyFaceDetector.loadFromDisk(modelPath),
        faceapi.nets.faceLandmark68Net.loadFromDisk(modelPath),
        faceapi.nets.faceRecognitionNet.loadFromDisk(modelPath),
      ]);

      modelLoadStatus = 'success';
    } catch (error: any) {
      modelLoadStatus = 'failed';
      modelLoadError = error.message;
    }

    return NextResponse.json({
      success: true,
      tests: {
        modelsDirectory: {
          exists: modelsExist,
          path: modelPath,
          files: modelFiles
        },
        referenceImages: {
          exists: refExist,
          path: refPath,
          files: refFiles
        },
        faceApi: {
          status: faceApiStatus,
          error: faceApiError
        },
        canvas: {
          status: canvasStatus,
          error: canvasError
        },
        environmentSetup: {
          status: envSetupStatus,
          error: envSetupError
        },
        modelLoading: {
          status: modelLoadStatus,
          error: modelLoadError
        }
      }
    });
    
  } catch (error: any) {
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
