# Face Recognition System Setup

## Project Structure
```
milan-face/
├── public/
│   ├── models/                 # Face detection models (already downloaded)
│   └── reference-images/       # Add your 50 reference images here
├── src/
│   └── app/
│       ├── api/face-recognition/
│       │   └── route.ts       # Face recognition API endpoint
│       └── page.tsx           # Main UI component
└── ...
```

## Setup Instructions

### 1. Add Reference Images
- Add your 50 reference images to `public/reference-images/` folder
- Supported formats: JPG, JPEG, PNG, GIF
- Make sure each image has a clear face visible
- Name them descriptively (e.g., `person1.jpg`, `john_doe.png`, etc.)

### 2. Install Dependencies
```bash
npm install
```

### 3. Run the Application
```bash
npm run dev
```

### 4. Open in Browser
Navigate to `http://localhost:3000`

## How to Use

### Camera Capture
1. Click "Start Camera" to access your webcam
2. Position your face in the camera view
3. Click "Capture Photo" to take a picture
4. Click "Recognize Face" to match against reference images

### File Upload
1. Click on the upload area or drag and drop an image
2. The system will automatically process the uploaded image
3. Results will show matched faces with confidence scores

## Features

- **Real-time Camera Capture**: Use your webcam to capture photos
- **File Upload**: Upload images from your device
- **Face Detection**: Automatically detects faces in images
- **Face Recognition**: Matches detected faces against reference database
- **Confidence Scoring**: Shows match confidence percentage
- **Local Processing**: All processing happens locally, no external APIs needed

## Technical Details

- **Framework**: Next.js 15 with TypeScript
- **Face Detection**: face-api.js with TensorFlow.js models
- **Models Used**:
  - Tiny Face Detector (for face detection)
  - Face Landmark 68 (for face landmarks)
  - Face Recognition (for face encoding)

## Troubleshooting

### No Reference Images Found
- Make sure you have images in `public/reference-images/` folder
- Check that image formats are supported (JPG, JPEG, PNG, GIF)

### Camera Not Working
- Grant camera permissions in your browser
- Make sure you're accessing the site via HTTPS or localhost

### Face Not Detected
- Ensure the face is clearly visible and well-lit
- Try different angles or lighting conditions
- Make sure the face is not too small in the image

### Low Match Confidence
- The system uses a threshold of 60% confidence
- You can adjust the threshold in `src/app/api/face-recognition/route.ts`
- Lower threshold = more matches but less accuracy
- Higher threshold = fewer matches but more accuracy

## Customization

### Adjust Recognition Threshold
Edit `src/app/api/face-recognition/route.ts`:
```typescript
const threshold = 0.6; // Change this value (0.0 to 1.0)
```

### Add More Models
You can add more sophisticated models by downloading them to `public/models/` and updating the API route.
