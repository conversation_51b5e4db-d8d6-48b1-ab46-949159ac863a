/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { isServer }) => {
    // Handle face-api.js module resolution issues
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        crypto: false,
      };
    }

    // Add externals for server-side rendering
    if (isServer) {
      config.externals = config.externals || [];
      config.externals.push({
        'face-api.js': 'commonjs face-api.js',
        canvas: 'commonjs canvas',
      });
    }

    return config;
  },
  
  // External packages for server-side rendering
  serverExternalPackages: ['face-api.js', 'canvas'],
};

module.exports = nextConfig;
