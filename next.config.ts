import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  webpack: (config, { isServer }) => {
    // Handle canvas and face-api.js dependencies for server-side
    if (isServer) {
      config.externals = config.externals || [];
      config.externals.push({
        'canvas': 'canvas',
        'face-api.js': 'face-api.js',
        'node-fetch': 'node-fetch'
      });
    }

    // Ignore problematic files
    config.resolve.fallback = {
      ...config.resolve.fallback,
      'aws-sdk': false,
      'mock-aws-s3': false,
      'nock': false,
    };

    // Ignore HTML and other non-JS files in node_modules
    config.module.rules.push({
      test: /\.html$/,
      loader: 'ignore-loader'
    });

    return config;
  },

  // Experimental features for better compatibility
  experimental: {
    serverComponentsExternalPackages: ['canvas', 'face-api.js', 'node-fetch']
  }
};

export default nextConfig;
