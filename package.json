{"name": "milan-face", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:webpack": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"canvas": "^3.1.2", "face-api.js": "^0.22.2", "next": "15.3.4", "node-fetch": "^3.3.2", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "ignore-loader": "^0.1.2", "tailwindcss": "^4", "typescript": "^5"}}